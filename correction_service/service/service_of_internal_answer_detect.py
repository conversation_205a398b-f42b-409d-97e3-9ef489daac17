# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanhui
# @Date         ：2024/6/15 14:26 
# @Description  : service_of_internal_answer_detect.py
import os.path
import re
import uuid
import cv2
import json
import time
import random
import asyncio
import traceback
from asteval import Interpreter

import numpy as np
from PIL import Image

from base_common.service.service_of_redis import RedisManager
from correction_service.common import CorrectionContext # update by dewen
from base_common.auto_setting import AutoSetting
from base_common.service.service_of_base import BaseService
from base_common.mission_mode import <PERSON><PERSON>lisa
from correction_service.answer_detect.data_of_response import ADResponse, ADPrepub
from correction_service.data import CorrectRequest, AnswerDetectRequest
from correction_service.util import AnswerDetectUtil
from base_common import LoggerFactory, RedisRequestor, Constants, ImageUtil, TopicType as TT, TopicTypeStr, Context

log = LoggerFactory.get_logger('IAnswerDetectService')
aeval = Interpreter()

need_hand_detect_list = [
    TT.SINGLE_CHOICE, TT.MULTI_CHOICE, TT.JUDGE_YES_NO,
    TT.FILL_BLANK, TT.OFF_COMPUTE, TT.WORD_PROBLEM, TT.ORAL_CALC,
    TT.VERTICAL_CALC, TT.VERTICAL_CALC_WITH_CHECK, -1
]
one_piece_box_size = 4
align_filter_enable = 0.0
def calc_iou(bbox1, bbox2):
    if not isinstance(bbox1, np.ndarray):
        bbox1 = np.array(bbox1)
    if not isinstance(bbox2, np.ndarray):
        bbox2 = np.array(bbox2)
    xmin1, ymin1, xmax1, ymax1, = np.split(bbox1, 4, axis=-1)
    xmin2, ymin2, xmax2, ymax2, = np.split(bbox2, 4, axis=-1)

    area1 = (xmax1 - xmin1) * (ymax1 - ymin1)
    area2 = (xmax2 - xmin2) * (ymax2 - ymin2)

    ymin = np.maximum(ymin1, np.squeeze(ymin2, axis=-1))
    xmin = np.maximum(xmin1, np.squeeze(xmin2, axis=-1))
    ymax = np.minimum(ymax1, np.squeeze(ymax2, axis=-1))
    xmax = np.minimum(xmax1, np.squeeze(xmax2, axis=-1))

    h = np.maximum(ymax - ymin, 0)
    w = np.maximum(xmax - xmin, 0)
    intersect = h * w

    iou_1 = intersect / area1
    iou_2 = intersect / area2.reshape(1, -1)
    ious_ = np.stack((iou_1, iou_2), 0)
    ious = ious_.max(0)
    return ious
    
def replace_mod_sym(str2):
    count = 0
    new_str = ''
    tmp_str = ''
    for c in str2:
        if c == '.':
            count += 1
            tmp_str += '.'
        elif c == '·':
            count += 1
            tmp_str += '·'
        elif c == '…':
            count += 3
            tmp_str += '…'
        else:
            if count > 3:
                new_str += '……'
            else:
                new_str += tmp_str
            new_str += c
            count = 0
            tmp_str = ''
            
    if count > 3:
        new_str += '……'
    else:
        new_str += tmp_str
    return new_str
    

def get_target_box(box,box_list_np,boxs_2p_tmp,areas):
    if len(box) == 0:
        return box
    box_area = (box[2]-box[0]) * (box[3]-box[1])
    ious = calc_iou([box],boxs_2p_tmp)
    max_ind = np.argsort(ious[0])[-1]

    if ious[0][max_ind] < 0.9:
        return box

    if areas[max_ind] < box_area*0.9 or areas[max_ind] > box_area*1.1:
        return box
    else:
        return box_list_np[max_ind]
def collect_intelligent_oral_calc_result(quickcal_results, item_id, ad_prep, ad_resp):
    #log.info(f"now try to collect intelligent oral calc result, item id as {item_id}")
    for i, item_result in enumerate(quickcal_results):
        for iv in range(len(item_result['reg_answers'])):
            item_result['reg_answers'][iv]['value'] = item_result['reg_answers'][iv]['value'].replace('P', '……')
        for iv in range(len(item_result['reg_answers'])):
            item_result['std_answers'][iv]['value'] = item_result['std_answers'][iv]['value'].replace('P', '……')
        item_k = item_id + '_' + str(i)
        ad_prep.get_res_boxs()[item_k] = item_result['box']
        ad_resp.get_stems()[item_k] = item_result['pred_str']
        if item_result['flag']:
            if not item_result['answer_flag']:
                ad_prep.get_res_types()[item_k] = 3
            else:
                ad_prep.get_res_types()[item_k] = 10
        else:
            ad_prep.get_res_types()[item_k] = 11
            ad_resp.get_recs()[item_k] = item_result['reg_answers']
            ad_resp.get_answers()[item_k] = item_result['std_answers']
def calc_word_problem(ad_prep, boxs_, box_ids_, boxs_list_4p):
    for ii, bb in enumerate(boxs_):
        boxs = AnswerDetectUtil.get_target_boxs(boxs_list_4p, bb, lenth=4)
        # 有时候用户的作答会超出题目区域，这时候需要将部分题目区域外的答案也算作本题的答题框
        boxs = AnswerDetectUtil.add_outside_answer(boxs_list_4p, boxs)

        if boxs is None or len(boxs) == 0:
            continue

        ad_prep.get_res_boxs().pop(box_ids_[ii])
        ad_prep.get_res_types().pop(box_ids_[ii])
        boxs_aligned = AnswerDetectUtil.align_boxs(boxs)
        for kk, bb1 in enumerate(boxs_aligned):
            ad_prep.get_res_boxs()[box_ids_[ii] + '_{}'.format(kk)] = bb1
            ad_prep.get_res_types()[box_ids_[ii] + '_{}'.format(kk)] = 0

def judge_shushi(reg_short,gt_answer):
    #reg_short:简化后的竖式识别结果
    #gt_answer:简化后的竖式答案
    diff = 1e3
    try:
        diff = abs(aeval.eval(reg_short.split('=')[-1]) - aeval.eval(gt_answer.split('=')[-1]))
    except:
        pass
        
    if reg_short.split('=')[-1] == gt_answer.split('=')[-1] or diff < 1e-4: # 与录入答案一致，则作答正确
        return True
    else:
        gt_item = gt_answer.split('=')[0]
        gt_a = gt_answer.split('=')[-1]

        try:
            if abs(aeval.eval(gt_item) - aeval.eval(gt_a)) > 1e-4:  # 处理约等于的竖式，不能严格要求计算竖式的识别结果和答案严格一致
                user_answer_short = reg_short.split('=')[-1].split('……')[0]
                gt_answer_short = gt_answer.split('=')[-1]
                if gt_answer_short.find('.') == -1:
                    length_gt_answer = 0
                else:
                    length_gt_answer = len(gt_answer_short) - gt_answer_short.rfind('.') - 1
                    
                if abs(round(aeval.eval(user_answer_short), length_gt_answer) - aeval.eval(gt_answer_short)) < 1e-6:
                    return True
        except:
            pass
    
        return False
def judge_item(item1,item2):
    diff = 1e3
    try:
        diff = abs(aeval.eval(item1.split('=')[0]) - aeval.eval(item2.split('=')[0]))
    except:
        pass
        
    if item1.split('=')[0] == item2.split('=')[0] or diff < 1e-4:
        return True
    else:
        return  False

def judge_answer_str(an1,an2):
    if len(an1) >= 3:
        if an2.startswith(an1):
            return True
        if an2.endswith(an1):
            return True
            
    if an1 == an2:
        return True

    diff = 1e3
    try:
        diff = abs(aeval.eval(an1) - aeval.eval(an2))
    except:
        pass
        
    if diff < 1e-6:
        return True
    else:
        return  False


def judge_yansuan_item(item1,item2):
    syms = ['+','-','*','/']
    sym = ''
    for ss  in syms:
        if item1.find(ss) != -1:
            sym = ss
    if item2.find(sym) == -1:
        return False
        
    try:
        cnt1_0,cnt1_1 = item1.split(sym)
        cnt2_0,cnt2_1 = item2.split(sym)
        
        diff1 = abs(aeval.eval(cnt1_0) - aeval.eval(cnt2_0))
        diff2 = abs(aeval.eval(cnt1_1) - aeval.eval(cnt2_1))
        
        if diff1 < 1e-6 and diff2 < 1e-6:
            return True
        else:
            return False
    except:
        return False


class IAnswerDetectService(BaseService):
    def __init__(self):
        super().__init__()
        self._ans_ser = None
        self._ocr_ser = None
        self.redis_service = RedisManager.get_mission_data_redis()
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()

    def collect_result(self, answer_detect, ad_prep, ad_resp):
        try:
            res_boxs, res_types = AnswerDetectUtil.remove_overlap(answer_detect.get_boxs(), ad_prep.get_res_boxs(),
                                                                  ad_prep.get_res_types())  # 因为映入了复合题，所以答案会存在交叉的情况，无法再过滤答题区域重叠的情况
            # res_boxs, res_types = ad_prep.get_res_boxs(), ad_prep.get_res_types()
            for k in res_boxs.keys():
                ad_resp.get_boxs()[k] = res_boxs[k]
            for k in res_types.keys():
                ad_resp.get_types()[k] = res_types[k]
            if Constants.SAVE_VIS:
                AnswerDetectUtil.vis_result(ad_prep.get_img_photo(), answer_detect.get_boxs(), answer_detect.get_items(),
                                            res_boxs, res_types, ad_prep.get_save_id(), Constants.VIS_RESULTS_PATH)
            ad_resp.set_flag(1000)
        except:
            log.error(f"error in ending!, due to {traceback.format_exc()}")

    def _check_item_box(self, img_photo_cv2, item_box, w, h):
        b0 = 0 if item_box[0] < 0 else item_box[0]
        b1 = 0 if item_box[1] < 0 else item_box[1]
        b2 = w if item_box[2] > w else item_box[2]
        b3 = h if item_box[3] > h else item_box[3]

        item_img_photo = img_photo_cv2[b1:b3, b0:b2, :]
        if item_img_photo is None or (item_img_photo.shape[0] < 10 or item_img_photo.shape[1] < 10):
            return True
        return False

    def _check_no_answer(self, boxs_list_4p, item_type, item_box, item_id, ad_prep):
        if item_type is None or item_type == -1:
            boxs = AnswerDetectUtil.get_target_boxs(boxs_list_4p, item_box, lenth=8)
            if len(boxs) == 0:
                ad_prep.get_res_boxs()[item_id + '_0'] = []
                ad_prep.get_res_types()[item_id + '_0'] = -1  # 空
            else:
                ad_prep.get_res_boxs()[item_id + '_0'] = boxs[0]
                ad_prep.get_res_types()[item_id + '_0'] = 0  # 0文本，1公式
            return True
        return False

    def _get_boxs_and_box_ids(self, item_id, ad_prep):
        boxs_ = []
        box_ids_ = []  # 提取该题对应的标准答案区域id
        answer_item_ids = enumerate(ad_prep.get_item_ids())
        for i, ii in answer_item_ids:
            if item_id == ii:
                boxs_.append(ad_prep.get_item_boxs()[i])
                box_ids_.append(ad_prep.get_item_box_ids()[i])
        return boxs_, box_ids_

    def _exec_vertical_with_check(self, ad_prep, ad_resp, answer_detect, box_ids_, boxs_):
        boxs_list_2p = ad_prep.get_boxs_list_2p()
        shushi_boxs = ad_prep.get_shushi_boxs()
        shushi_regs = ad_prep.get_shushi_regs()
        for ii, bb in enumerate(boxs_):
            boxs_handwrite = AnswerDetectUtil.get_target_boxs(boxs_list_2p, bb)
            '''
            if len(boxs_handwrite) == 0:
                ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                ad_prep.get_res_types()[box_ids_[ii]] = -1
                continue
            '''

            shushi_target_boxs,shushi_target_ious = AnswerDetectUtil.get_target_boxs(shushi_boxs, bb,iou_thr=0.1,return_iou=True)  # 找到和答题box有交叠的竖式
            shushi_target_ious = np.array(shushi_target_ious)    
            if len(shushi_target_boxs) == 0:  # 如果作答区域未返回答案，直接返回未作答
                ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                ad_prep.get_res_types()[box_ids_[ii]] = -1
                continue

            yansuan = answer_detect.get_verify_cal()[box_ids_[ii]]
            gt_answer = answer_detect.get_items_answer()[box_ids_[ii]]

            gt_item = gt_answer.split('=')[0]
            ad_resp.get_stems()[box_ids_[ii]] = gt_item
            #题干出现了两个运算符的时候，特殊处理，在合理竖式作答范围内，找到某个竖式的结果答案=本题录入的答案，即判对，反之判错。不管验算。
            #log.info(f"{gt_item} : {gt_item.count('+') +gt_item.count('-')+gt_item.count('*')+gt_item.count('/')}")
            if gt_item.count('+') +gt_item.count('-')+gt_item.count('*')+gt_item.count('/') > 1:
                gt_a = gt_answer.split('=')[1]
                
                judge_flag = False
                for item_bb in shushi_target_boxs:
                    reg = shushi_regs[shushi_boxs.index(item_bb)]
            
                    if reg.find('/') == -1:
                        reg_short = ''
                        for reg_item_i, reg_item in enumerate(reg.split('=')):  # 考虑了多步骤的竖式云端
                            if reg_item_i == 0:
                                reg_short += reg_item
                                continue
                            elif reg_item_i == len(reg.split('=')) - 1:
                                reg_short += ('=' + reg.split('=')[reg_item_i])
                            else:
                                if reg_item.find('+') != -1:
                                    reg_short += ('+' + reg_item.split('+')[-1])
                                elif reg_item.find('-') != -1:
                                    reg_short += ('-' + reg_item.split('-')[-1])
                                else:
                                    continue
                    else:
                        regs = reg.split('S')
                        if len(regs) < 2:
                            continue
                        reg_short = regs[0] + '=' + regs[1].split('T')[0]
                        if reg.split('H')[-1] != '0':
                            reg_short += ('……' + reg.split('H')[-1])
                            
                    reg_a = reg_short.split('=')[-1]
                    
                    if reg_a == gt_a:
                        judge_flag = True
                        break
                        
                    try:
                        if abs(aeval.eval(reg_a) - aeval.eval(gt_a)) < 1e-6:
                            judge_flag = True
                            break
                    except:
                        pass
                        
                if judge_flag:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = 10
                else:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = 11
                    
                    ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                    ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]

            elif not yansuan:  # 不需要验算
                if len(shushi_target_ious) > 0 and shushi_target_ious.min() < 0.3:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = -1
                else:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = 11
                    ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                    ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                
                iou_record = 0.
                match_flag = False
                for item_ind,item_bb in enumerate(shushi_target_boxs):  # 因为答案区域可能会有其他的竖式，所以遍历所有的竖式计算式子
                    reg = shushi_regs[shushi_boxs.index(item_bb)]  # 竖式的识别结果
                
                    if reg.find('/') == -1:
                        reg_short = ''
                        for reg_item_i, reg_item in enumerate(reg.split('=')):  # 考虑了多步骤的竖式云端
                            if reg_item_i == 0:
                                reg_short += reg_item
                                continue
                            elif reg_item_i == len(reg.split('=')) - 1:
                                reg_short += ('=' + reg.split('=')[reg_item_i])
                            else:
                                if reg_item.find('+') != -1:
                                    reg_short += ('+' + reg_item.split('+')[-1])
                                elif reg_item.find('-') != -1:
                                    reg_short += ('-' + reg_item.split('-')[-1])
                                else:
                                    continue
                    else:
                        regs = reg.split('S')
                        if len(regs) < 2:
                            continue
                        reg_short = regs[0] + '=' + regs[1].split('T')[0]
                        if reg.split('H')[-1] != '0':
                            reg_short += ('……' + reg.split('H')[-1])
                    #log.info(f'reg_short,gt_answer:{reg_short} {gt_answer}')
                    reg_short, gt_answer, reg = AnswerDetectUtil.blur_shushi_item(reg_short, gt_answer, reg)
                    item_diff = 1e3
                    try:
                        item_diff = abs(aeval.eval(reg_short.split('=')[0]) - aeval.eval(gt_answer.split('=')[0]))
                    except:
                        pass
                        
                    #judge_item_flag = (reg_short.split('=')[0] == gt_answer.split('=')[0])
                    judge_item_flag = True if item_diff < 1e-3 else False
                    judge_flag = judge_shushi(reg_short,gt_answer)
                    # log.info(f'reg_short,gt_answer,judge_item_flag,judge_flag:{reg_short} {gt_answer} {judge_item_flag} {judge_flag}')
                    if judge_item_flag:
                        if judge_flag:
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 10
                            break
                        else:
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 11
        
                            ad_resp.get_recs()[box_ids_[ii]] = [{'value': reg, 'type': '0'}]
                            ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                            
                            match_flag = True
                    else:
                        item_iou = shushi_target_ious[item_ind]
                        if item_iou > iou_record and not match_flag:
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 11
        
                            ad_resp.get_recs()[box_ids_[ii]] = [{'value': reg, 'type': '0'}]
                            ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                            
                            iou_record = item_iou
                        
                        

            else:  # 需要验算
                if len(shushi_target_ious) > 0 and shushi_target_ious.min() < 0.3:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = -1
                else:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = bb
                    ad_prep.get_res_types()[box_ids_[ii]] = 11
                    
                    ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                    ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]

                for item_bb in shushi_target_boxs:  # 因为答案区域可能会有其他的竖式，所以遍历所有的竖式计算式子
                    reg = shushi_regs[shushi_boxs.index(item_bb)]  # 竖式的识别结果
                    if reg.find('/') == -1:
                        reg_short = ''
                        for reg_item_i, reg_item in enumerate(reg.split('=')):  # 考虑了多步骤的竖式云端
                            if reg_item_i == 0:
                                reg_short += reg_item
                                continue
                            elif reg_item_i == len(reg.split('=')) - 1:
                                reg_short += ('=' + reg.split('=')[reg_item_i])
                            else:
                                if reg_item.find('+') != -1:
                                    reg_short += ('+' + reg_item.split('+')[-1])
                                elif reg_item.find('-') != -1:
                                    reg_short += ('-' + reg_item.split('-')[-1])
                                else:
                                    continue
                    else:
                        regs = reg.split('S')
                        if len(regs) < 2:
                            continue
                        reg_short = regs[0] + '=' + regs[1].split('T')[0]
                        if reg.split('H')[-1] != '0':
                            reg_short += ('……' + reg.split('H')[-1])
                            
                    reg_short, gt_answer, reg = AnswerDetectUtil.blur_shushi_item(reg_short, gt_answer, reg)
                    
                    item_diff = 1e3
                    try:
                        item_diff = abs(aeval.eval(reg_short.split('=')[0]) - aeval.eval(gt_answer.split('=')[0]))
                    except:
                        pass
                        
                    #judge_item_flag = (reg_short.split('=')[0] == gt_answer.split('=')[0])
                    judge_item_flag = True if item_diff < 1e-3 else False
                    judge_flag = judge_shushi(reg_short,gt_answer)
                    
                    #log.info(f'reg_short,gt_answer,judge_item_flag,judge_flag:{reg_short} {gt_answer} {judge_item_flag} {judge_flag}')
                    if  judge_item_flag and not judge_flag:
                        # 找到了对应的竖式计算式子，且计算错误，不关心验算，直接返回错误
                        ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                        ad_prep.get_res_types()[box_ids_[ii]] = 11

                        ad_resp.get_recs()[box_ids_[ii]] = [{'value': reg, 'type': '0'}]
                        ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                        break
                    elif judge_item_flag and judge_flag:  # 竖式计算正确,检查验算的情况
                        yansuan_items = AnswerDetectUtil.get_yansuan_item(gt_answer)
                        #log.info(f'yansuan_items:{yansuan_items}')
                        if len(shushi_target_boxs) < 2: #如果答题区域内竖式数量少于2，默认没有竖式验算作答
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 12
                            ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                            ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                            break 

                        yansuan_judge = False
                        for yansuan_bb in shushi_target_boxs:
                            reg_yansuan = shushi_regs[shushi_boxs.index(yansuan_bb)]
                            if reg_yansuan.find('/') == -1:
                                reg_yansuan_short = ''
                                for reg_item_i, reg_item in enumerate(reg_yansuan.split('=')):  # 考虑了多步骤的竖式运算
                                    if reg_item_i == 0:
                                        reg_yansuan_short += reg_item
                                        continue
                                    elif reg_item_i == len(reg.split('=')) - 1:
                                        reg_yansuan_short += ('=' + reg.split('=')[reg_item_i])
                                    else:
                                        if reg_item.find('+') != -1:
                                            reg_yansuan_short += ('+' + reg_item.split('+')[-1])
                                        elif reg_item.find('-') != -1:
                                            reg_yansuan_short += ('-' + reg_item.split('-')[-1])
                                        else:
                                            continue
                            else:
                                reg_yansuan_split = reg_yansuan.split('S')
                                if len(reg_yansuan_split) >= 2:
                                    reg_yansuan_short = reg_yansuan_split[0] + '=' + reg_yansuan_split[1].split('T')[0]
                                    if reg_yansuan.split('H')[-1] != '0':
                                        reg_yansuan_short += ('……' + reg_yansuan.split('H')[-1])
                                else:
                                    reg_yansuan_short = reg_yansuan

                            for yansuan_item in yansuan_items:
                                reg_yansuan_short, yansuan_item = AnswerDetectUtil.blur_shushi_item(reg_yansuan_short,yansuan_item)

                                user_answer = reg_yansuan_short.split('=')[0]
                                std_answer = yansuan_item.split('=')[0]
                                
                                if AnswerDetectUtil.edit_distance_optimized(gt_answer.split('=')[0],user_answer) < 2:#过滤掉原竖式对验算判定的影响
                                    continue
                                #log.info(f'user_answer:{user_answer}; std_answer:{std_answer}')
                                    
                                if judge_yansuan_item(user_answer,std_answer):
                                    yansuan_judge = True
                                    break
                                
                                if std_answer.find('*') != -1 and std_answer.find('+') != -1:  # 带余数验算包含1*2即可                                   
                                    if judge_yansuan_item(user_answer,std_answer.split('+')[0]):
                                        yansuan_judge = True
                                        break
                                    if std_answer.startswith(user_answer) and user_answer.find('*') != -1:
                                        yansuan_judge = True
                                        break

                            if yansuan_judge:
                                break
                        if yansuan_judge:
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 10
                        else:
                            ad_prep.get_res_boxs()[box_ids_[ii]] = item_bb
                            ad_prep.get_res_types()[box_ids_[ii]] = 12
                            ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                            ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
                        break
                    else:
                        continue
    async def _pre_detect(self, mission_id, answer_detect, ad_prep, img_src, img_key, img_type):
        items_type = answer_detect.get_items_type()
        img_name = answer_detect.get_url_photo().split('/')[-1]
        item_types_value = items_type.values()
        need_line_param = None
        #################################
        # 处理连线题型                    #
        #################################
        if TT.CONNECT_LINE in item_types_value:
            data_json = answer_detect.to_dict()
            data_json.update({'img_name': img_name, 'img_key': img_key, 'img_type': img_type})
            need_line_param = json.dumps(data_json)

        # 获取题目信息
        items = answer_detect.get_items()
        # 只有当本页有需要做手写检测的题型时，才会进行手写字的检测
        item_keys = items.keys()
        need_detect_param = None
        for item_id in item_keys:
            item_type = items_type[item_id]
            if item_type is None or item_type in need_hand_detect_list:  # 27是带等号的竖式计算题
                need_detect_param = {'img_name': img_name, 'img_key': img_key, 'img_type': img_type}
                break

        need_vertical_param = None
        # 获取竖式计算题的答案
        if TT.VERTICAL_CALC in item_types_value or TT.VERTICAL_CALC_WITH_CHECK in item_types_value:
            need_vertical_param = {'img_name': img_name, 'img_key': img_key, 'img_type': img_type}

        futures = [
            self._exec_line_async(mission_id, need_line_param),
            self._exec_detect_async(mission_id, need_detect_param),
            self._exec_vertical_async(mission_id, need_vertical_param, img_src)
        ]
        results = await asyncio.gather(*futures)
        # 获取连线题结果
        line_res, line_boxs, line_types = results[0]
        # 获取手写识别结果
        _, boxs_list_4p, boxs_list_2p, boxs_list_np = results[1]
        # 获取竖式计算结果
        vertical_res, shushi_boxs, shushi_regs = results[2]

        if line_res:
            boxs_keys = line_boxs.keys()
            if len(boxs_keys) != 0:
                for k in boxs_keys:
                    ad_prep.get_res_boxs()[k] = line_boxs[k]
                    ad_prep.get_res_types()[k] = line_types[k]

        # 跳过连线题
        items_type = {k: v for k, v in items_type.items() if v != TT.CONNECT_LINE}
        img_photo_cv2 = img_src.copy()
        img_photo = Image.fromarray(cv2.cvtColor(img_photo_cv2, cv2.COLOR_BGR2RGB))
        #img_photo = Image.fromarray(img_photo_cv2)
        h, w, _ignored = img_photo_cv2.shape

        ad_prep.set_w(w)
        ad_prep.set_h(h)
        ad_prep.set_img_name(img_name)
        ad_prep.set_item_keys(item_keys)
        ad_prep.set_items_type(items_type)
        ad_prep.set_img_photo_cv2(img_photo_cv2)
        ad_prep.set_img_photo(img_photo)
        ad_prep.set_save_id(random.randint(0, 100))
        ad_prep.set_boxs_list_4p(boxs_list_4p)
        ad_prep.set_boxs_list_2p(boxs_list_2p)
        ad_prep.set_boxs_list_np(boxs_list_np)
        ad_prep.set_shushi_boxs(shushi_boxs)
        ad_prep.set_shushi_regs(shushi_regs)
        return ad_prep

    async def _exec_line_async(self, mission_id, data_json):
        if data_json is None:
            return False, None, None
        t0 = time.time()
        response_json = await RedisRequestor.ai_line_calc(data_json, Context.get_record_id(mission_id))
        Context.report_cost(mission_id, (MissionAlisa.LINE_SERVICE, time.time() - t0))
        line_res = response_json.get_json_response()
        if line_res is None or 'boxs' not in line_res or 'types' not in line_res:
            return False, None, None
        line_boxs = line_res['boxs']
        line_types = line_res['types']
        return True, line_boxs, line_types

    async def _exec_detect_async(self, mission_id, data_json):
        if data_json is None:
            log.info(f"(mission_id: {mission_id}) 无需答案检测")
            return False, None, None, None
        t0 = time.time()
        response_json = await RedisRequestor.ai_answer_detect(data_json, Context.get_record_id(mission_id))
        Context.report_cost(mission_id, (MissionAlisa.ANSWER_DETECT_SERVICE, time.time() - t0))
        response_json = response_json.get_json_response()
        if response_json is None:
            log.error(f"(mission_id: {mission_id}) 答案检测失败")
            return False, None, None, None
        boxs_list_4p = response_json['boxs_list_4p']
        boxs_list_2p = response_json['boxs_list_2p']
        boxs_list_np = response_json.get('boxs_list_np', [])
        return True, boxs_list_4p, boxs_list_2p, boxs_list_np

    async def _exec_vertical_async(self, mission_id, params, img_src):
        if params is None:
            return False, None, None
        t0 = time.time()
        data_json = json.dumps(params)
        response_json = await RedisRequestor.ai_vertical_det_calc(data_json, Context.get_record_id(mission_id))
        new_boxes_list = response_json.get_json_response()
        Context.report_cost(mission_id, (MissionAlisa.VERTICAL_DET_SERVICE, time.time() - t0))
        shushi_boxs = []
        shushi_regs = []
        if len(new_boxes_list) == 0:
            log.warn("没有检测到任何box")
            return False, shushi_boxs, shushi_regs
        if Constants.SAVE_VIS:
            ImageUtil.save_vertical_det(img_src, new_boxes_list)
        t0 = time.time()
        futures = []
        for i in range(len(new_boxes_list)):
            params.update({'new_boxes_list': [new_boxes_list[i]]})
            data_json = json.dumps(params)
            futures.append(RedisRequestor.ai_vertical_rec_calc(data_json, Context.get_record_id(mission_id)))
            
        result = await asyncio.gather(*futures)
        Context.report_cost(mission_id, (MissionAlisa.VERTICAL_REC_SERVICE, time.time() - t0))

        for resp in result:
            if resp.is_success():
                response_json = resp.get_json_response()
                shushi_boxs.extend(response_json['shushi_boxs'])
                shushi_regs.extend(response_json['shushi_regs'])
        if Constants.SAVE_VIS:
            ImageUtil.save_vertical_rec(img_src, shushi_boxs, shushi_regs)
        return True, shushi_boxs, shushi_regs

    def req_answer_detect(self, req_data: CorrectRequest, answer_detect: AnswerDetectRequest, user_img, img_key, img_type) -> ADResponse:
        return asyncio.run(self.async_req_answer_detect(req_data, answer_detect, user_img, img_key, img_type))
    async def async_req_answer_detect(self, req_data: CorrectRequest, answer_detect: AnswerDetectRequest, user_img, img_key, img_type) -> ADResponse:
        """
        result_json格式:
        {
        'flag':1000,
        'boxs': {
                'itemid_boxid':[x1,y1,x2,y2],
                ...
            }
            #应用题返回格式为
            {
                ...,
                itemid_boxid_0:[[x,y]x4],
                itemid_boxid_1:[[x,y]x4],
                ...
            }   
            
            
            #如果该题没有检测到答案，则
            {
                ...,
                itemid_boxid：[],
                ...
            }
        }
        """
        global align_filter_enable
        self.update_batch_size()
        ad_resp = ADResponse(flag=1001)
        mission_id = req_data.get_mission_id()
        #if answer_detect.get_surf_mat() > 1e-5:
        ori_box = answer_detect.get_boxs().copy()
        surf_variance = answer_detect.get_surf_mat()
        filter_flag = surf_variance >= align_filter_enable
        if filter_flag:
            #  总统计surf方差数量：134376
            # 百分位：
            # 5% = 0.000256     10% = 0.000567  15% = 0.000952  20% = 0.001392  25% = 0.001897  30% = 0.002447
            # 35% = 0.003058    40% = 0.003756  45% = 0.004529  50% = 0.005375  55% = 0.006335  60% = 0.007459
            # 65% = 0.008749    70% = 0.010301  75% = 0.012213  80% = 0.014672  85% = 0.018026  90% = 0.023079  95% = 0.033566
            # 这里设置0.01是希望只有30%的图片进行答案对齐
            try:
                tmp_img_key, tmp_img_type = await self._update_answer_detect(answer_detect, mission_id, user_img, img_key, img_type)
                if tmp_img_key is not None:
                    img_key = tmp_img_key
                if tmp_img_type is not None:
                    img_type = tmp_img_type
            except:
                log.error(f"答案位置对齐失败\n{traceback.format_exc()}")
                answer_detect.set_boxs(ori_box)
        ad_prep = ADPrepub.from_dict(answer_detect.get_boxs())
        if not ad_prep.is_success():
            return ad_resp
        if not Context.is_product():
            log.info(f"(mission_id: {mission_id}) 答案检测请求: {answer_detect.to_dict()}")

        await self._pre_detect(mission_id, answer_detect, ad_prep, user_img, img_key, img_type)
        escapeCalcAlgorithmJudge = req_data.get_escapeCalcAlgorithmJudge()
        await self._exec_items(mission_id, answer_detect, ad_prep, ad_resp, user_img, img_key, img_type, escapeCalcAlgorithmJudge)
        self.collect_result(answer_detect, ad_prep, ad_resp)
        if not Context.is_product():
            log.info(f"(mission_id: {mission_id}) 答案检测结果: {ad_resp.to_string()}")
        return ad_resp

    async def _update_answer_detect(self, answer_detect: AnswerDetectRequest, mission_id: str, user_img, img_key, img_type):
        t0 = time.time()
        pdf_path = answer_detect.get_pdf_path()
        if pdf_path is None or not os.path.exists(pdf_path):
            return None, None

        # log.info(f"answer_detect_data ######:{answer_detect.to_json()}")
        items_paint_ids = []  # 作图题特殊处理
        items_box = []
        items_type = answer_detect.get_items_type()
        for key_id in items_type.keys():
            if items_type[key_id] == 21:
                items_paint_ids.append(key_id)
                items_box.append(answer_detect.get_items()[key_id])

        items_answer = answer_detect.get_items_answer()
        items_line_ids = []  # 连线题特殊处理
        items_line_box = []
        boxs_line_add = {}
        for key_id in items_type.keys():
            if items_type[key_id] == 9:
                items_line_ids.append(key_id)
                i_box = answer_detect.get_items()[key_id]

                wi = i_box[2] - i_box[0]
                hi = i_box[3] - i_box[1]

                items_line_box.append(i_box)

                item_answer = items_answer[key_id]
                for ii, i_answer in enumerate(item_answer):
                    block_name = i_answer['blockName']
                    answer = i_answer['answer']
                    for jj, an in enumerate(answer):
                        try:
                            k = an['value']
                            area_an = an['sourceAnswer']['ops'][0]['insert']['line']['area']
                            region = [area_an['x1'], area_an['y1'], area_an['x2'], area_an['y2']]
                            box_jj = [i_box[0] + int(region[0] * wi), i_box[1] + int(region[1] * hi),
                                      i_box[0] + int(region[2] * wi), i_box[1] + int(region[3] * hi)]
                            boxs_line_add[block_name + '_' + k] = box_jj
                        except:
                            # log.info(f"(mission_id: {mission_id}) i_answer $$$$$$$$$$$$$$ :{an}")
                            pass
                            
        boxs_ori = answer_detect.get_boxs()
        boxs_ori.update(boxs_line_add)
        if img_key is None:
            img_key, img_type = self.set_image(user_img)
        req_data = {
            'pdf_path': pdf_path,
            'img_type': img_type,
            'img_key': img_key,
            'boxs_ori': boxs_ori,
            'photo_crop': answer_detect.get_photo_crop(),
            'temp_crop': answer_detect.get_temp_crop()
        }
        api_response = await RedisRequestor.ai_zone_align(req_data, Context.get_record_id(mission_id))
        Context.report_cost(mission_id, (MissionAlisa.ANSWER_ALIGN_SERVICE, time.time() - t0))

        if api_response.is_success():
            align_boxs = api_response.get_json_response()
            answer_detect.set_boxs(align_boxs)

            # 作图题需要对items_answer里面的坐标位置进行重新赋值，才能生效
            for ii, paint_id in enumerate(items_paint_ids):
                item_box = items_box[ii]
                hh = item_box[3] - item_box[1]
                ww = item_box[2] - item_box[0]
                items_answer_pid = items_answer[paint_id]
                for i_answer in items_answer_pid:
                    try:
                        block_name = i_answer['blockName']
                        box_name = block_name + '_0'
                        align_box = align_boxs[box_name]
                        align_box_normal = [(align_box[0] - item_box[0]) / ww, (align_box[1] - item_box[1]) / hh,
                                            (align_box[2] - item_box[0]) / ww, (align_box[3] - item_box[1]) / hh]
    
                        i_answer['area'][0]['x1'] = align_box_normal[0]
                        i_answer['area'][0]['y1'] = align_box_normal[1]
                        i_answer['area'][0]['x2'] = align_box_normal[2]
                        i_answer['area'][0]['y2'] = align_box_normal[3]
                    except:
                        # log.info(f"(mission_id: {mission_id}) i_answer ################1:{i_answer}")
                        pass
                        
                items_answer[paint_id] = items_answer_pid

            for ii, line_id in enumerate(items_line_ids):
                item_box = items_line_box[ii]
                hh = item_box[3] - item_box[1]
                ww = item_box[2] - item_box[0]
                items_answer_pid = items_answer[line_id]
                for i_answer in items_answer_pid:
                    try:
                        block_name = i_answer['blockName']
                        box_name = block_name + '_0'
                        align_box = align_boxs[box_name]
                        align_box_normal = [(align_box[0] - item_box[0]) / ww, (align_box[1] - item_box[1]) / hh,
                                            (align_box[2] - item_box[0]) / ww, (align_box[3] - item_box[1]) / hh]
    
                        i_answer['area'][0]['x1'] = align_box_normal[0]
                        i_answer['area'][0]['y1'] = align_box_normal[1]
                        i_answer['area'][0]['x2'] = align_box_normal[2]
                        i_answer['area'][0]['y2'] = align_box_normal[3]
                    except:
                        log.error(f"(mission_id: {mission_id}) i_answer ################2:{i_answer}")
                        pass

                    sub_answers = i_answer['answer']
                    for jj, an in enumerate(sub_answers):
                        try:
                            k = an['value']
                            box_k_name = block_name + '_' + k
                            box_k = align_boxs[box_k_name]
                            del align_boxs[box_k_name]
                            regn = [(box_k[0] - item_box[0]) / ww, (box_k[1] - item_box[1]) / hh,
                                    (box_k[2] - item_box[0]) / ww, (box_k[3] - item_box[1]) / hh]
                            an['sourceAnswer']['ops'][0]['insert']['line']['area']['x1'] = regn[0]
                            an['sourceAnswer']['ops'][0]['insert']['line']['area']['y1'] = regn[1]
                            an['sourceAnswer']['ops'][0]['insert']['line']['area']['x2'] = regn[2]
                            an['sourceAnswer']['ops'][0]['insert']['line']['area']['y2'] = regn[3]
                        except:
                            log.error(f"(mission_id: {mission_id}) an ################3:{an}")
                            pass

                    i_answer['answer'] = sub_answers

                items_answer[line_id] = items_answer_pid

            answer_detect.set_items_answer(items_answer)
        return img_key, img_type
        # log.info(f"answer_detect_data $$$$$$$:{answer_detect.to_json()}")
    async def _exec_items(self, mission_id, answer_detect, ad_prep, ad_resp, src_img, img_key, img_type, escapeCalcAlgorithmJudge):
        items = answer_detect.get_items()
        item_keys = ad_prep.get_item_keys()
        items_type = ad_prep.get_items_type()
        boxs_list_4p = ad_prep.get_boxs_list_4p()
        boxs_list_2p = ad_prep.get_boxs_list_2p()
        img_photo_cv2 = ad_prep.get_img_photo_cv2()
        w = ad_prep.get_w()
        h = ad_prep.get_h()

        vertical_params = {}
        vertical_ch_params = {}
        iac_params = {'img_key': img_key, 'img_type': img_type}
        need_iac = False
        draw_params = []
        all_item_info = {}
        for item_id in item_keys:
            if item_id not in items_type:
                continue
            item_type = items_type[item_id]
            item_box = items[item_id]  # 获取题目区域
            item_box_expand = [0]*4
            item_box_expand[0] = max(0,item_box[0]-100)
            item_box_expand[1] = max(0,item_box[1]-100)
            item_box_expand[2] = min(w,item_box[2]+100)
            item_box_expand[3] = min(h,item_box[3]+100)
            
            all_item_info[str(item_id)] = TopicTypeStr.get(item_type, '未知')
            ######################
            # 检查没有录题的题目有没有作答，以便前端决定是否显示答案
            ######################
            if self._check_no_answer(boxs_list_4p, item_type, item_box, item_id, ad_prep):
                if item_type == TT.INTELLIGENT_ORAL_CALC:
                    log.info(f"(mission_id: {mission_id}) 智能口算题 无答案")
                continue

            if self._check_item_box(img_photo_cv2, item_box, w, h):
                if item_type == TT.INTELLIGENT_ORAL_CALC:
                    log.info(f"(mission_id: {mission_id}) 智能口算题 无box")
                continue

            item_type = int(item_type)
            # 提取该题对应的标准答案区域boxs 提取该题对应的标准答案区域id
            boxs_, box_ids_ = self._get_boxs_and_box_ids(item_id, ad_prep)

            # 在此处做题型判断，并调用不同的模型
            #################################
            # 处理竖式计算题型                #
            #################################
            # 'items_answer':{'box_id':'1+1=2'}
            # 返回标签：10对；11错；12半对；-1未作答
            # 带等号的竖式计算
            if item_type == TT.VERTICAL_CALC:
                for ii, bb in enumerate(boxs_):
                    box_id = box_ids_[ii]
                    gt_answer = answer_detect.get_items_answer()[box_id]  # 完整的竖式计算式子
                    gt_item = gt_answer.split('=')[0]
                    ad_resp.get_stems()[box_id] = gt_item
                    
                    bb_tmp = bb.copy() # 将等号后面的答题区域向左扩充一点
                    ww_b = bb_tmp[2] -  bb_tmp[0]
                    bb_tmp[0] -= min(20,ww_b)
                    boxs = AnswerDetectUtil.get_target_boxs(boxs_list_2p, bb_tmp)  # 获取等号后面的用户手写答案
                    if len(boxs) == 0:  # 如果作答区域未找到手写答案，赋值为作答区
                        ii_box = bb
                    else:
                        ii_box = AnswerDetectUtil.choose_n_1_box(boxs, bb_tmp)  # 处理可能遇到的一个答案位置检测到多个手写文本的情况
                    ii_img = img_photo_cv2[ii_box[1]:ii_box[3], ii_box[0]:ii_box[2], :]
                    # 调用文本识别模型，识别用户作答
                    vertical_ch_id = str(uuid.uuid4())
                    
                    if len(boxs) == 0:
                        vertical_params.update({
                            vertical_ch_id: {
                                'box_id': box_id, 'ii': ii, 'bb': bb,
                                'ii_box': ii_box, 'box_ids_': box_ids_.copy(),'empty_hw':True
                            }
                        })
                    else:
                        vertical_params.update({
                            vertical_ch_id: {
                                'box_id': box_id, 'ii': ii, 'bb': bb,
                                'ii_box': ii_box, 'box_ids_': box_ids_.copy(),'empty_hw':False
                            }
                        })
                    vertical_ch_params.update({
                        vertical_ch_id: {
                            'recordId': vertical_ch_id, 'imgs': [ImageUtil.numpy_2_base64(ii_img)],
                            'std_answers': [gt_answer.split('=')[-1]]
                        }
                    })
                continue

            # 仅竖式计算式子
            if item_type == TT.VERTICAL_CALC_WITH_CHECK:
                self._exec_vertical_with_check(ad_prep, ad_resp, answer_detect, box_ids_, boxs_)
                continue

            #################################
            # 处理口算题型                   #
            #################################
            if item_type == TT.INTELLIGENT_ORAL_CALC:
                keys = list(ad_prep.get_res_boxs().keys())
                for kk in keys:  # 剔除口算题的boxs
                    if kk.startswith(item_id):
                        ad_prep.get_res_boxs().pop(kk)
                        ad_prep.get_res_types().pop(kk)
                iac_params[item_id] = {
                    'id': item_id,
                    'box': self.calc_oral_item_box(item_box, w, h, items=items, current_item_id=item_id)
                }
                need_iac = True
                continue

            if len(boxs_) == 0:
                continue
            #################################
            # 处理作图题型                  #
            #################################
            if item_type == TT.DRAW_IMG:
                w_item = item_box[2] - item_box[0]
                h_item = item_box[3] - item_box[1]
                ww = w_item if w_item < 100 else 100
                hh = h_item if h_item < 100 else 100
                w_gap = int(ww * 0.2)
                h_gap = int(hh * 0.2)
                b0 = 0 if item_box[0] - w_gap < 0 else item_box[0] - w_gap
                b1 = 0 if item_box[1] - h_gap < 0 else item_box[1] - h_gap
                b2 = w if item_box[2] + w_gap > w else item_box[2] + w_gap
                b3 = h if item_box[3] + h_gap > h else item_box[3] + h_gap

                item_img_photo = img_photo_cv2[b1:b3, b0:b2, :]
                if len(answer_detect.get_items_answer()[item_id]) != 0:
                    answer = AnswerDetectUtil.extract_paint(answer_detect.get_items_answer()[item_id], item_box,
                                                            (b0, b1, b2, b3))
                    draw_params.append({
                        'answer': answer,
                        'img_base64': ImageUtil.numpy_2_base64(item_img_photo),
                        'bb': (b0, b1)
                    })
                continue

            #################################
            # 处理应用题型                   #
            #################################
            # 应用题题型，可增加其他题型，如果是应用题的话
            if item_type == TT.WORD_PROBLEM:
                calc_word_problem(ad_prep, boxs_, box_ids_, boxs_list_4p)
                continue

            #################################
            # 处理拖式计算，先整道题检测，然后再把答案分配到对应题目 #
            #################################
            if item_type == TT.OFF_COMPUTE:
                # update by dewen
                boxs_h = sum(box[3] - box[1] for box in boxs_) / len(boxs_)
                tmp_results_compute = []
                # update by dewen
                # 脱式计算题编号，待修改
                results_compute = []
                if escapeCalcAlgorithmJudge is None or escapeCalcAlgorithmJudge == False:
                    for index, bb_ in enumerate(boxs_):
                       tmp_bb_ = bb_.copy()  # update by dewen
                       ww_ = bb_[2] - bb_[0]  # 拖式计算答案重心更靠左边，box适当左移
                       tmp_bb_[0] = max(0, bb_[0] - int(ww_ * 0.05))
                       tmp_bb_[2] = bb_[2] - int(ww_ * 0.05)
                       bb_r = AnswerDetectUtil.get_target_boxs(boxs_list_4p, tmp_bb_, lenth=4, iou_thr=0.3)
                       results_compute.append(bb_r)
                    for ii, result_compute in enumerate(results_compute):
                       if len(result_compute) == 0:
                           continue
                
                       y_max = 0
                       bottom_index = 0
                       for kk, bb in enumerate(result_compute):
                          y_place = (bb[2][1] + bb[3][1]) / 2
                          if y_place > y_max:
                              y_max = y_place
                              bottom_index = kk
                
                       ad_prep.get_res_boxs()[box_ids_[ii]] = result_compute[bottom_index]
                       ad_prep.get_res_types()[box_ids_[ii]] = 0
                    continue
                else:
                    for index, bb_ in enumerate(boxs_):
                        area_is_empty = False
                        bb_is_empty = AnswerDetectUtil.get_target_boxs(boxs_list_4p, bb_, lenth=4, iou_thr=0.3)
                        if len(bb_is_empty) == 0:
                            area_is_empty = True
                        tmp_bb_ = bb_.copy() # update by dewen
                        ww_ = bb_[2] - bb_[0]  # 拖式计算答案重心更靠左边，box适当左移
                        tmp_bb_[0] = max(0, bb_[0] - int(ww_ * 0.05))
                        tmp_bb_[2] = bb_[2] + int(ww_ * 0.15) 
                        if index + 1 <= len(boxs_)-1 and boxs_[index+1][0] >= boxs_[index][2]:
                            if tmp_bb_[2] > boxs_[index+1][0]:
                                tmp_bb_[2] = boxs_[index+1][0]
                        tmp_bb_r = AnswerDetectUtil.get_target_boxs(boxs_list_4p, tmp_bb_, lenth=4, iou_thr=0.3) # update by dewen
                        # 有时候用户的作答会超出题目区域，这时候需要将部分题目区域外的答案也算作本题的答题框
                        #bb_r = AnswerDetectUtil.add_outside_answer(boxs_list_4p, bb_r, tuoshi_flag=True)
                        
                        current_idx = boxs_.index(bb_)
                        # 提取目标矩形的x1和y1
                        tx1, ty1, _, _ = bb_
                        candidates = []
                        candidates2 = []
                        
                        for rect in boxs_[current_idx+1:]:
                            x1, y1, _, _ = rect
                            # 判断条件：x1相近 且 位于下方
                            if abs(x1 - tx1) <= 40 and y1 > ty1:
                                candidates.append(rect)
                                
                        for rect in boxs_[current_idx+1:]:
                            x1, y1, _, _ = rect
                            # 判断条件：y1相近 且 位于右方
                            if abs(y1 - ty1) <= 20 and x1 > tx1:
                                candidates2.append(rect)
                    
                        # 按y1升序排序（最近的在下方的矩形排最前）
                        candidates.sort(key=lambda x: x[1])
                        
                        next_boxs_y1 = candidates[0][1] if candidates else None
                        
                        if boxs_h < 50:
                            tmp_bb_[3] = bb_[3] + (int(boxs_h * (7/2)))
                        elif 50 <= boxs_h <= 80:
                            tmp_bb_[3] = bb_[3] + (int(boxs_h * 3))
                        elif 80 <= boxs_h <= 120:
                            tmp_bb_[3] = bb_[3] + (int(boxs_h * 2))
                        elif 120 <= boxs_h <= 150:
                            tmp_bb_[3] = bb_[3] + int(boxs_h)
                        elif 150 <= boxs_h <= 180:
                            tmp_bb_[3] = bb_[3] + (int(boxs_h * 2/5))
                        elif bb_[3] + (int(boxs_h) * 2) > h:
                            tmp_bb_[3] = h
                        else:
                            tmp_bb_[3] = bb_[3] + (int(boxs_h * 1/5))
                        if str(int(item_id)+1) in items:
                            if items[str(int(item_id)+1)][1] >= items[str(int(item_id))][3]:
                                if tmp_bb_[3] > items[str(int(item_id)+1)][1]:
                                    tmp_bb_[3] = items[str(int(item_id)+1)][1] + 75
                        if index + 3 <= len(boxs_)-1 and boxs_[index+3][1] >= boxs_[index][3]:
                            if tmp_bb_[3] > boxs_[index+3][1]:
                                tmp_bb_[3] = boxs_[index+3][1] + 10
                        if index + 2 <= len(boxs_)-1 and boxs_[index+2][1] >= boxs_[index][3]:
                            if tmp_bb_[3] > boxs_[index+2][1]:
                                tmp_bb_[3] = boxs_[index+2][1] + 10
                        if index + 1 <= len(boxs_)-1 and boxs_[index+1][1] >= boxs_[index][3]:
                            if tmp_bb_[3] > boxs_[index+1][1]:
                                tmp_bb_[3] = boxs_[index+1][1] + 20
                        if tmp_bb_[3] < boxs_[index][3]:
                            tmp_bb_[3] = boxs_[index][3] + 40
                        if area_is_empty:
                            tmp_bb_[3] = bb_[3]
                        # 如果超出同列下一行的作答区，扩至下一作答区的上框
                        if next_boxs_y1 is not None and tmp_bb_[3] >= next_boxs_y1:
                            tmp_bb_[3] = next_boxs_y1 - 5
                        bb_r = AnswerDetectUtil.get_target_boxs(boxs_list_4p, tmp_bb_, lenth=4, iou_thr=0.3)
                        reversed_bb_r = bb_r[::-1]
                        mask_reversed_bb_r = [True] * len(reversed_bb_r)
                        for i in range(1, len(reversed_bb_r)):
                            if reversed_bb_r[i][0][1] - reversed_bb_r[i-1][3][1] > 70:
                                mask_reversed_bb_r[i] = False
                                for j in range(i, len(reversed_bb_r)):
                                    mask_reversed_bb_r[j] = False
                                break
                        reversed_bb_r = np.array(reversed_bb_r)
                        bb_r = reversed_bb_r[mask_reversed_bb_r].tolist()[::-1]
                        # update by dewen
                        results_compute.append(bb_r)
            
                    for ii, result_compute in enumerate(results_compute):
                        if len(result_compute) == 0:
                            continue
                        ad_prep.get_res_boxs()[box_ids_[ii]] = results_compute[ii] # update by dewen
                        ad_prep.get_res_types()[box_ids_[ii]] = 0
                    continue

            #############################################
            # 处理选择判断题                             #
            #############################################
            preds_font = AnswerDetectUtil.get_target_boxs(boxs_list_2p, item_box_expand)
            if len(preds_font) == 0:
                continue

            boxs, preds, preds_label = AnswerDetectUtil.concate_or_split_boxs(boxs_, preds_font, [0] * len(preds_font))
            #if len(boxs) <= 30:
            # 使用IOU算法匹配
            preds_box, preds_labels_final = AnswerDetectUtil.match_res(
                np.array(boxs, dtype=np.int32),
                np.array(preds, dtype=np.int32),
                np.array(preds_label, dtype=np.int32))
            # else:
            #     # 使用匈牙利算法匹配
            #     preds_box, preds_labels_final = AnswerDetectUtil.match_res_hungrain(
            #         np.array(boxs, dtype=np.int32),
            #         np.array(preds, dtype=np.int32),
            #         np.array(preds_label, dtype=np.int32))
            boxs_list_np = ad_prep.get_boxs_list_np()
            areas = []
            boxs_2p_tmp = []

            for bb in boxs_list_np:
                bb = np.array(bb, dtype=np.int32)
                xmin = bb[:, 0].min()
                xmax = bb[:, 0].max()
                ymin = bb[:, 1].min()
                ymax = bb[:, 1].max()

                areas.append((ymax - ymin) * (xmax - xmin))
                boxs_2p_tmp.append([xmin, ymin, xmax, ymax])

            for k, box_id_ in enumerate(box_ids_):
                ad_resp.get_np_boxs()[box_id_] = get_target_box(preds_box[k], boxs_list_np, boxs_2p_tmp, areas)
                ad_prep.get_res_boxs()[box_id_] = AnswerDetectUtil.adjust_box_ratio(preds_box[k])
                ad_prep.get_res_types()[box_id_] = 0
        if not Context.is_product():
            log.info(f"(mission_id: {mission_id}) 所有题型{json.dumps(all_item_info, ensure_ascii=False)}")
        futures = []
        if len(vertical_params) != 0:
            futures.append(self._exec_vertical(mission_id, answer_detect, ad_prep, ad_resp, vertical_params, vertical_ch_params))
        if need_iac:
            futures.append(self._exec_iac(mission_id, ad_prep, ad_resp, iac_params, src_img))
        if len(draw_params) != 0:
            futures.append(self._exec_draw(mission_id, answer_detect, ad_prep, draw_params))
        await asyncio.gather(*futures)
    def update_batch_size(self):
        global one_piece_box_size, align_filter_enable
        try:
            config = AutoSetting.get_config()
            one_piece_box_size = config['one_piece_quick_rec_size']
            align_filter_enable = config['align_filter_enable']
        except:
            one_piece_box_size = 4
            align_filter_enable = 0.0
    async def _exec_draw(self, mission_id, answer_detect, ad_prep, data_json):
        t0 = time.time()
        model_response = await RedisRequestor.ai_graphics_calc(data_json, Context.get_record_id(mission_id))
        if model_response.is_success():
            resp_datas = model_response.get_json_response()
            for i in range(len(resp_datas)):
                paint_results = resp_datas[i]
                box_ids_key = answer_detect.get_boxs().keys()
                paint_boxs = paint_results['boxs']
                paint_types = paint_results['types']
                paint_ids_key = paint_boxs.keys()
                if len(paint_ids_key) == 0:
                    continue
                for k in paint_ids_key:
                    for k_b in box_ids_key:
                        if k_b.startswith(k):
                            ad_prep.get_res_boxs()[k_b] = paint_boxs[k]
                            ad_prep.get_res_types()[k_b] = paint_types[k]
        Context.report_cost(mission_id, (MissionAlisa.GRAPHICS_SERVICE, time.time() - t0))

    async def _exec_iac(self, mission_id, ad_prep, ad_resp, iac_params, src_img):
        t0 = time.time()
        model_response = await RedisRequestor.ai_quick_calc_detect(iac_params, Context.get_record_id(mission_id))
        Context.report_cost(mission_id, (MissionAlisa.QUICK_DET_SERVICE, time.time() - t0))
        t0 = time.time()
        resp = {}
        if model_response.is_success():
            resp = model_response.get_json_response()
        if len(resp.keys()) == 0:
            return
        rec_boxes = []
        futures = []
        for item_id in resp.keys():
            rec_boxs = resp[item_id]
            try:
                rec_boxes.append({
                    'bb': iac_params[item_id]['box'],
                    'rec_boxs': rec_boxs
                })
            except:
                pass
            if len(rec_boxs) != 0:
                data_len = len(rec_boxs)
                start_index = 0
                index = 0
                length = data_len // one_piece_box_size if data_len % one_piece_box_size == 0 else data_len // one_piece_box_size + 1
                while start_index < data_len:
                    end_index = min(data_len, start_index + one_piece_box_size)
                    quick_params = {
                        'item_id': item_id,
                        'index': index,
                        'box': iac_params[item_id]['box'],
                        'img_key': iac_params['img_key'],
                        'img_type': iac_params['img_type'],
                        'rec_boxs': rec_boxs[start_index:end_index],
                        'length': length
                    }
                    futures.append(RedisRequestor.ai_quick_calc_rec(json.dumps(quick_params), Context.get_record_id(mission_id)))
                    start_index += one_piece_box_size
                    index += 1

        if len(futures) == 0:
            return

        if Constants.SAVE_VIS:
            ImageUtil.save_quick_det(src_img, rec_boxes)

        resps = await asyncio.gather(*futures)
        Context.report_cost(mission_id, (MissionAlisa.QUICK_REC_SERVICE, time.time() - t0))
        final_ = {}
        rec_res = []
        for item in resps:
            if item.is_success():
                resp_data = item.get_json_response()
                for item_id in resp_data:
                    item = resp_data[item_id]
                    if item_id not in final_:
                        final_[item_id] = [None] * item['length']
                    final_[item_id][item['index']] = item['final_results']
                    rec_res.extend(item['rec_res'])

        final_1 = {}
        for item_id in final_:
            final_1[item_id] = []
            final_result = final_[item_id]
            for i in range(len(final_result)):
                if final_result[i]:
                    final_1[item_id].extend(final_result[i])

        for item_id in final_1:
            #log.info(f"(mission_id: {mission_id}) final_results: {json.dumps(final_1[item_id], ensure_ascii=False)}")
            collect_intelligent_oral_calc_result(final_1[item_id], item_id, ad_prep, ad_resp)

        if Constants.SAVE_VIS:
            ImageUtil.save_quick_rec(src_img, rec_res)
    async def _exec_vertical(self, mission_id, answer_detect, ad_prep, ad_resp, vertical_params, vertical_ch_params):
        t0 = time.time()
        futures = []
        tasks = {}
        for vertical_ch_id in vertical_ch_params.keys():
            data_json = vertical_ch_params[vertical_ch_id]
            futures.append(RedisRequestor.ai_ocr_hand_write_cn(data_json, Context.get_record_id(mission_id)))
            tasks.update({len(futures) - 1: vertical_ch_id})
        model_resps = await asyncio.gather(*futures)

        Context.report_cost(mission_id, (MissionAlisa.CH_HW_SERVICE, time.time() - t0))
        #log.info(f'(mission_id: {mission_id}) len(model_resps): {len(model_resps)}')
        for i in range(len(model_resps)):
            if model_resps[i].is_success():
                vertical_ch_id = tasks[i]
                pred_chars = model_resps[i].get_json_response()[0]
                try:
                    self.__end_vertical(pred_chars, answer_detect, ad_prep, ad_resp, vertical_params[vertical_ch_id])
                except:
                    log.error(f'{pred_chars} 处理失败 {traceback.format_exc()}')

    def __end_vertical(self, pred_chars, answer_detect, ad_prep, ad_resp, params):
        box_id = params['box_id']
        ii_box = params['ii_box'] 
        box_ids_ = params['box_ids_']
        ii = params['ii']
        bb = params['bb']
        shushi_regs = ad_prep.get_shushi_regs()
        shushi_boxs = ad_prep.get_shushi_boxs()
        
        target_shushi_boxs = AnswerDetectUtil.get_target_boxs(shushi_boxs, bb)
        
        # 调用文本识别模型，识别用户作答
        gt_answer = answer_detect.get_items_answer()[box_id]  # 完整的竖式计算式子
        gt_item = gt_answer.split('=')[0]
        yansuan = answer_detect.get_verify_cal()[box_id]  # 是否需要验算的标签，0不验算；1要验算
        yansuan_flag = yansuan != 0

        pred_chars = replace_mod_sym(pred_chars)
        if params['empty_hw']:#等号后面未匹配到手写字时，将预测字符串定义为''
            pred_chars = ''
            
        # log.info(f'@@@@@@@@@@@@@@@ gt_answer: {gt_answer},  pred_chars:{pred_chars}')
        #log.info(f'(mission_id: {mission_id}) shushi_regs: {shushi_regs}')
        diff = 1e3
        try:
            diff = abs(float(gt_answer.split('=')[-1]) - float(pred_chars))
        except:
            pass
            
        if not judge_answer_str(gt_answer.split('=')[-1],pred_chars) and diff > 1e-5:
            # 当文本识别答案和标注答案不一致的时候，需要参考用户的竖式的识别结果，如果竖式识别结果是对的，也需要判对。
            for shushi_i, reg in enumerate(shushi_regs):
                shushi_box_i = shushi_boxs[shushi_i]
                shushi_i_h = shushi_box_i[3] - shushi_box_i[1]
                #log.info(f'(mission_id: {mission_id}) reg: {reg}')
                if shushi_box_i[1] - ii_box[1] < -1 * shushi_i_h * 0.9:  # 式子不能高于答案位置太多
                    continue
                if ((ii_box[0] - shushi_box_i[2]) ** 2 + (
                        ii_box[0] - shushi_box_i[2]) ** 2) ** 0.5 > shushi_i_h * 2:
                    # 式子的右上点与答案位置的左下点的距离不能太大
                    continue
                #log.info(f'(mission_id: {mission_id}) reg: {reg}')
                if reg.find('/') == -1:
                    reg_short = reg.split('=')[0]  # 只保留竖式计算的题干，1+1的形式
                    reg_ans = reg.split('=')[-1]
                else:
                    reg_short = reg.split('S')[0]
                    if reg.split('H')[-1] in ['0','H']:
                        reg_ans = reg.split('S')[-1].split('T')[0]
                    else:
                        reg_ans = reg.split('S')[-1].split('T')[0] + '……' + reg.split('H')[-1]
                
                reg_ans = AnswerDetectUtil.environ_answers_by_answer(gt_answer,
                                                                     reg_ans)  # 处理约等于的时候竖式和录入结果没有字符集对齐的情况
                #log.info(f'################ gt_answer: {gt_answer}；reg_ans: {reg_ans}')
                if AnswerDetectUtil.edit_distance_optimized(gt_item, reg_short) <= 2:
                    if judge_answer_str(gt_answer.split('=')[-1],reg_ans):
                        pred_chars = reg_ans
                        ii_box = bb
                        break
                try:
                    gt_item_tmp = AnswerDetectUtil.change_forume_short(
                        gt_item)  # 需要考虑用户在写竖式的时候，乘数被乘数 以及 加数被加数 互换了书写的位置
                    if AnswerDetectUtil.edit_distance_optimized(gt_item_tmp, reg_short) <= 2:
                        if judge_answer_str(gt_answer.split('=')[-1],reg_ans):
                            pred_chars = reg_ans
                            ii_box = bb
                            break
                except:
                    pass
        aa = gt_answer.split('=')[-1]
        if len(pred_chars) == 0:#等号后面未匹配到手写答案，且没有找到竖式（上面的逻辑）
            ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
            ad_prep.get_res_types()[box_ids_[ii]] = -1
        
        elif judge_answer_str(gt_answer.split('=')[-1] , pred_chars) or diff < 1e-5:  # 答案正确
            if not yansuan_flag:  # 不需要验算的时候
                ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                ad_prep.get_res_types()[box_ids_[ii]] = 10
            else:# 答案正确且需要验算
                yansuan_items = AnswerDetectUtil.get_yansuan_item(
                    gt_answer)  # 将1+1=2改成2-1=1的验算格式，验算暂时不支持余数的形式
                yansuan_judge = False

                for shushi_i, reg in enumerate(shushi_regs):  # 竖式识别结果里面查找是否有验算
                    shushi_box_i = shushi_boxs[shushi_i]
                    shushi_i_h = shushi_box_i[3] - shushi_box_i[1]
                    if shushi_box_i[1] - ii_box[1] < -1 * shushi_i_h * 0.9:  # 验算式子不能高于答案位置太多
                        continue
                    if ((ii_box[0] - shushi_box_i[2]) ** 2 + (
                            ii_box[0] - shushi_box_i[2]) ** 2) ** 0.5 > shushi_i_h * 4:
                        # 验算式子的右上点与答案位置的左下点的距离不能太大
                        continue

                    # 将竖式识别结果简化程1+1=2的形式
                    if reg.find('/') == -1 and reg.find('=') != -1:  # 验算式子没有/号的是时候。
                        if reg.find('*') != -1 and reg.find('+') != -1:  # 主要是为了有余数除法的验算
                            reg_short = reg.split('=')[0] + '+' + reg.split('=')[-2].split('+')[
                                1] + '=' + \
                                        reg.split('=')[-1]
                        else:
                            reg_short = reg.split('=')[0] + '=' + reg.split('=')[-1]
                    else:
                        regs = reg.split('S')
                        if len(regs) < 2:
                            continue
                        reg_short = regs[0] + '=' + regs[1].split('T')[0]  # 验算为除号的式子不考虑余数

                    for yansuan_item in yansuan_items:
                        reg_short, yansuan_item = AnswerDetectUtil.blur_shushi_item(reg_short,yansuan_item)
                                                                                        
                        user_answer = reg_short.split('=')[0]
                        std_answer = yansuan_item.split('=')[0]

                        #log.info(f'######################gt_item: {gt_item} user_answer: {user_answer}')
                        if AnswerDetectUtil.edit_distance_optimized(gt_item,user_answer) < 2:#过滤掉原竖式对验算判定的影响
                            continue
                            
                        if judge_yansuan_item(user_answer,std_answer):
                            yansuan_judge = True
                            break

                        if std_answer.find('*') != -1 and std_answer.find('+') != -1:  # 带余数验算包含1*2即可                                   
                            if judge_yansuan_item(user_answer,std_answer.split('+')[0]):
                                yansuan_judge = True
                                break
                            if std_answer.startswith(user_answer) and user_answer.find('*') != -1:
                                yansuan_judge = True
                                break

                    if yansuan_judge:
                        break
                if yansuan_judge:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                    ad_prep.get_res_types()[box_ids_[ii]] = 10
                else:
                    ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                    ad_prep.get_res_types()[box_ids_[ii]] = 12  # 如果没有正常匹配到验算就打半勾
                    ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]
                    ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]
        else:  # 如果等号后面的结果就识别错误
            gt_item = gt_answer.split('=')[0]
            find_shushi_flag = False  # 是否匹配到对应的竖式计算式子
            for shushi_i, reg in enumerate(shushi_regs):

                shushi_box_i = shushi_boxs[shushi_i]
                shushi_i_h = shushi_box_i[3] - shushi_box_i[1]
                if shushi_box_i[1] - ii_box[1] < -1 * shushi_i_h * 0.9:  # 验算式子不能高于答案位置太多
                    continue
                if ((ii_box[0] - shushi_box_i[2]) ** 2 + (
                        ii_box[0] - shushi_box_i[2]) ** 2) ** 0.5 > shushi_i_h * 2:
                    # 验算式子的右上点与答案位置的左下点的距离不能太大
                    continue

                if reg.find('/') == -1:
                    reg_short = reg.split('=')[0]  # 只保留竖式计算的题干，1+1的形式
                else:
                    reg_short = reg.split('S')[0]

                if AnswerDetectUtil.edit_distance_optimized(gt_item,
                                                   reg_short) <= 2:  # 如果找到了题干相同的竖式，则需要返回这个竖式的识别字符串
                    ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                    ad_prep.get_res_types()[box_ids_[ii]] = 11

                    ad_resp.get_recs()[box_ids_[ii]] = [
                        {'value': reg.replace(reg_short, gt_item), 'type': 0}]  # 表示的是识别到的竖式识别的题干
                    ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': 0}]
                    find_shushi_flag = True
                    break
                try:
                    gt_item_tmp = AnswerDetectUtil.change_forume_short(
                        gt_item)  # 需要考虑用户在写竖式的时候，乘数被乘数 以及 加数被加数 互换了书写的位置
                    if AnswerDetectUtil.edit_distance_optimized(gt_item_tmp,
                                                       reg_short) <= 2:  # 如果找到了题干相同的竖式，则需要返回这个竖式的识别字符串
                        ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                        ad_prep.get_res_types()[box_ids_[ii]] = 11

                        ad_resp.get_recs()[box_ids_[ii]] = [
                            {'value': reg.replace(reg_short, gt_item_tmp),
                             'type': 0}]  # 表示的是识别到的竖式识别的题干
                        ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': 0}]
                        find_shushi_flag = True
                        break
                except:
                    pass

            if not find_shushi_flag:  # 如果没有找到本题的竖式
                ad_prep.get_res_boxs()[box_ids_[ii]] = [int(ii) for ii in ii_box]
                ad_prep.get_res_types()[box_ids_[ii]] = 11

                ad_resp.get_recs()[box_ids_[ii]] = [{'value': '', 'type': '0'}]  # 答案错误，且未发现竖式的时候 返回空
                ad_resp.get_answers()[box_ids_[ii]] = [{'value': gt_answer, 'type': '0'}]

    def calc_oral_item_box(self, item_box, w, h, items=None, current_item_id=None):
        # 题目区域的宽度
        w_item = item_box[2] - item_box[0]
        # 题目区域的高度
        h_item = item_box[3] - item_box[1]
        max_w_item = 100
        max_h_item = 100
        ww = w_item if w_item < max_w_item else max_w_item
        hh = h_item if h_item < max_h_item else max_h_item
        # 扩展宽度的百分比
        gap_w_ratio = 0.5
        # 扩展高度的百分比
        gap_h_ratio = 0.45
        w_gap = int(ww * gap_w_ratio)
        h_gap = int(hh * gap_h_ratio)
        b0 = 0 if item_box[0] - w_gap < 0 else item_box[0] - w_gap
        b1 = 0 if item_box[1] - h_gap < 0 else item_box[1] - h_gap
        b2 = w if item_box[2] + w_gap > w else item_box[2] + w_gap
        b3 = h if item_box[3] + h_gap > h else item_box[3] + h_gap

        multi_items = len(items.keys()) > 1

        # 如果单页有多个题目，需要检查当前扩展的边界是否与其他口算题的边界重叠
        if multi_items and current_item_id:
            # 获取所有口算题的box
            other_item_boxes = []
            for item_key, item_value in items.items():
                # 跳过当前处理的item
                if item_key == current_item_id:
                    continue
                other_item_boxes.append(item_value)

            # 计算当前item的X轴中点
            current_center_x = (item_box[0] + item_box[2]) / 2
            # 设置X轴距离阈值，使用题目宽度的30%作为阈值
            x_threshold = w_item * 0.3

            # 检查上边界是否与其他口算题的下边界重叠
            for other_box in other_item_boxes:
                current_center_y = (item_box[1] + item_box[3]) / 2
                other_center_x = (other_box[0] + other_box[2]) / 2
                other_center_y = (other_box[1] + other_box[3]) / 2

                # 检查两个box是否在同一列（X轴距离是否在阈值内）
                x_distance = abs(current_center_x - other_center_x)
                if x_distance > x_threshold:
                    # 如果X轴距离大于阈值，认为不在同一列，跳过检查
                    continue

                # 如果当前box在其他box的下方
                if current_center_y > other_center_y:
                    # 如果扩展后的上边界超过了其他box的下边界
                    if b1 < other_box[3]:
                        # 将上边界调整为其他box的下边界
                        b1 = other_box[3]

                # 如果当前box在其他box的上方
                if current_center_y < other_center_y:
                    # 如果扩展后的下边界超过了其他box的上边界
                    if b3 > other_box[1]:
                        # 将下边界调整为其他box的上边界
                        b3 = other_box[1]

        return [b0, b1, b2, b3]
    
    # update by dewen
    def tuoshi_correct(self, answer_items, req_data):
        final_results_dict = {}
        reg_coords_keys = []
        for key in answer_items.get_reg_coords():
            reg_coords_keys.append(key)
        items_answer = {}
        for key in answer_items.get_items_type():
            if answer_items.get_items_type()[key] == 18:
                items_answer[key] = answer_items.get_items_answer()[key]
        answer_dict = {}
        for item_id in items_answer:
            for ii_box in items_answer[item_id]:
                blockName = ii_box['blockName']
                blockAnswer = ii_box['answer'][0]['value']
                for item in reg_coords_keys:
                    if item.startswith(blockName):
                        answer_dict[item] = blockAnswer
        # 5385596_0_0
        for item in answer_dict:
            final_results_dict[item] = {'final_results': [], 'pred_strs_list': [], 'pred_boxs_list': [], 'index': 0,
                                        'length': 1, 'rec_res': []}

        mission_id = req_data.get_mission_id()
        correction_data = CorrectionContext.get_correct_data(mission_id)
        correction_res = correction_data.get_total_recs()
        for box_ids_ in correction_res:
            if len(box_ids_.split('_')) >= 3:
                tmp_box = box_ids_.split('_')[0] + '_' + box_ids_.split('_')[1] + '_' + box_ids_.split('_')[2]
                if box_ids_.startswith(tmp_box) and tmp_box in answer_dict:
                    correction_value = correction_res[box_ids_]
                    answer_value = answer_dict[tmp_box]
                    tmp_box_result = {}

                    tmp_box_result['pred_str'] = [{'value': '', 'type': 1}]
                    tmp_box_result['box'] = answer_items.get_reg_coords()[tmp_box]
                    tmp_box_result['reg_answers'] = correction_value
                    tmp_box_result['std_answers'] = \
                    answer_items.get_items_answer()[tmp_box.split('_')[0]][int(tmp_box.split('_')[1])]['answer'][0]['value']
                    tmp_box_result['flag'] = True
                    tmp_box_result['answer_flag'] = False
                    if "(1)" in correction_value:
                        correction_value = correction_value.replace("(1)", "")
                    if "(2)" in correction_value:
                        correction_value = correction_value.replace("(2)", "")
                    if "(3)" in correction_value:
                        correction_value = correction_value.replace("(3)", "")
                    if "(4)" in correction_value:
                        correction_value = correction_value.replace("(4)", "")
                    if "(5)" in correction_value:
                        correction_value = correction_value.replace("(5)", "")
                    if "(6)" in correction_value:
                        correction_value = correction_value.replace("(6)", "")
                    if "(7)" in correction_value:
                        correction_value = correction_value.replace("(7)", "")
                    if "(8)" in correction_value:
                        correction_value = correction_value.replace("(8)", "")
                    if "(9)" in correction_value:
                        correction_value = correction_value.replace("(9)", "")
                    if "(10)" in correction_value:
                        correction_value = correction_value.replace("(10)", "")
                    if correction_value == answer_value:
                        tmp_box_result['flag'] = True
                        tmp_box_result['answer_flag'] = True
                    if '-' in correction_value:
                        if correction_value.split('-')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '÷' in correction_value:
                        if correction_value.split('÷')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '<' in correction_value:
                        if correction_value.split('<')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if ':' in correction_value:
                        if correction_value.split(':')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '二' in correction_value:
                        if correction_value.split('二')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '三' in correction_value:
                        if correction_value.split('三')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '三.' in correction_value:
                        if correction_value.split('三.')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '2' in correction_value:
                        if correction_value.split('2')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '{' in correction_value and '}' in correction_value:
                        if correction_value.replace('{', '').replace('}', '') == answer_value.replace('{', '').replace('}', ''):
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '=' in correction_value:
                        answer_value = re.sub(r'\\mathrm\{\s*　\s*\}', '', answer_value)
                        if correction_value.split('=')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True 
                        elif AnswerDetectUtil.compare_fraction_and_decimal(answer_value, correction_value.split('=')[-1]):
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                        elif AnswerDetectUtil.compare_latex_fraction_decimal(answer_value.replace('-',''), correction_value.split('=')[-1].replace('-','')):
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                        elif AnswerDetectUtil.compare_latex_fraction_decimal(correction_value.split('=')[-1].replace('-',''), answer_value.replace('-','')):
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                        elif AnswerDetectUtil.compare_math_expressions(correction_value.split('=')[-1], answer_value):
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                        elif correction_value.split('=')[-1].replace('.','') == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                        else:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = False
                    if '=÷' in correction_value:
                        if correction_value.split('=÷')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '1±' in correction_value:
                        if correction_value.split('1±')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True
                    if '1王' in correction_value:
                        if correction_value.split('1王')[-1] == answer_value:
                            tmp_box_result['flag'] = True
                            tmp_box_result['answer_flag'] = True

                    # 5385596_0_0
                    final_results_dict[tmp_box]['pred_boxs_list'].append(tmp_box_result['box'])
                    tmp_rec_res = {'box': tmp_box_result['box'], 'pred': correction_value}
                    final_results_dict[tmp_box]['rec_res'].append(tmp_rec_res)
                    final_results_dict[tmp_box]['final_results'].append(tmp_box_result)

        final_result = {}
        for item in final_results_dict:
            grouped = {}
            final_result[item] = {'final_results': [], 'pred_strs_list': [], 'pred_boxs_list': [], 'index': 0,
                                  'length': 1, 'rec_res': []}
            for tmp_box_result in final_results_dict[item]['final_results']:
                box_key = tuple(tmp_box_result['box'])
                grouped.setdefault(box_key, []).append(tmp_box_result)

            for group in grouped.values():
                # 排序：answer_flag降序，pred_str长度升序
                sorted_group = sorted(group, key=lambda x: (-x['answer_flag'], len(x['pred_str'])))
                final_result[item]['final_results'].append(sorted_group[0])
            final_result[item]['pred_strs_list'] = final_results_dict[item]['pred_strs_list']
            final_result[item]['pred_boxs_list'] = final_results_dict[item]['pred_boxs_list']
            final_result[item]['index'] = answer_items.get_items_answer()[item.split('_')[0]][int(item.split('_')[1])][
                'orders']
            final_result[item]['length'] = len(final_results_dict[item]['pred_boxs_list'])
            final_result[item]['rec_res'] = final_results_dict[item]['rec_res']

        total_recs = correction_data.get_total_recs()
        for box_id in list(total_recs):
            if box_id in answer_dict:
                total_recs.pop(box_id)

        tmp_reg_class = correction_data.get_reg_class()
        for k in final_result:
            total_recs[k] = final_result[k]
            if final_result[k]['final_results'][0]['answer_flag']:
                tmp_reg_class[k] = 10
            if final_result[k]['final_results'][0]['answer_flag'] == False:
                tmp_reg_class[k] = 11
            if final_result[k]['final_results'][0]['reg_answers'] == '':
                tmp_reg_class[k] = -2

        correction_data.set_reg_class(tmp_reg_class)
        correction_data.set_total_recs(total_recs)
